<template>
  <div class="med-container">
    <!-- Ant Design Vue 中文配置 -->
    <a-config-provider :locale="antdLocale">
      <n-config-provider :locale="zhCN" :date-locale="dateZhCN" :hljs="hljs" :theme-overrides="themeOverrides">
        <!-- 消息提示 -->
        <n-message-provider>
          <!-- 对话框 -->
          <n-dialog-provider>
            <!-- 模态框 -->
            <n-modal-provider>
              <!-- 通知 -->
              <n-notification-provider>
                <!-- 消息处理提供者 -->
                <MessageHandlerProvider>
                  <!-- 路由视图 -->
                  <router-view />
                  <!-- 消息注册 -->
                  <Register />
                </MessageHandlerProvider>
              </n-notification-provider>
            </n-modal-provider>
          </n-dialog-provider>
        </n-message-provider>
      </n-config-provider>
    </a-config-provider>
    <!-- 注册 -->

    <!-- 流程详情弹窗 -->
    <ProcessInstanceDetailModal
      v-if="showProcessDetail"
      :processInstanceId="processInstanceId"
      v-model:show="showProcessDetail"
    />

    <!-- 全局文件预览组件 -->
    <!-- <FilePreviewModal v-if="showFilePreview" v-model:show="showFilePreview" :file="previewFile" /> -->
  </div>
</template>

<script lang="ts">
  import Register from '@comps/register/register.vue'
  import { darkTheme, dateZhCN, GlobalThemeOverrides, zhCN } from 'naive-ui'
  import { ConfigProvider } from 'ant-design-vue'
  import zhCN_antd from 'ant-design-vue/es/locale/zh_CN'
  import ThemeOverrides from '@/types/comps/themeOverrides'
  import { useResponsiveTheme } from '@/composables/useResponsiveTheme'
  import hljs from 'highlight.js/lib/core'
  import javascript from 'highlight.js/lib/languages/javascript'
  import xml from 'highlight.js/lib/languages/xml'
  import json from 'highlight.js/lib/languages/json'
  import { defineComponent, ref, onMounted, onUnmounted, computed, provide } from 'vue'
  import { useEventBus } from '@/utils/eventBus'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'
  import MessageHandlerProvider from '@/components/message/MessageHandlerProvider.vue'
  import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'
  import { isMobileDevice as isMobileDeviceFn, responsiveListener, DeviceType } from '@/utils/device'
  import { useRouter } from 'vue-router'

  hljs.registerLanguage('javascript', javascript)
  hljs.registerLanguage('xml', xml)
  hljs.registerLanguage('json', json)

  hljs.registerLanguage('naive-log', () => ({
    contains: [
      {
        className: 'number',
        begin: /\d+/,
      },
      {
        className: 'error',
        begin: /错误/,
      },
      {
        className: 'missing',
        begin: /\[参数缺失\]/,
      },
      {
        className: 'complete',
        begin: /完成/,
      },
      {
        className: 'pmsParam',
        begin: /\$\{([^}]+)\}/g,
      },
    ],
  }))

  //@ts-ignore
  window.require = (url: string) => {
    url = url.replace('@/', '')
    return new URL(url, import.meta.url).href
  }
  console.log(import.meta.env)

  export default defineComponent({
    components: {
      Register,
      AdaptiveDataTable,
      ProcessInstanceDetailModal,
      MessageHandlerProvider,
      'a-config-provider': ConfigProvider,
    },
    setup() {
      // 使用响应式主题
      const { themeOverrides: responsiveThemeOverrides, deviceClasses } = useResponsiveTheme()

      // 路由实例
      const router = useRouter()

      // 合并默认主题和响应式主题
      const themeOverrides = computed(() => {
        return {
          ...ThemeOverrides,
          // ...responsiveThemeOverrides.value,
        }
      })

      // 流程详情弹窗状态
      const showProcessDetail = ref(false)
      const processInstanceId = ref('')

      // 移动端检测
      const isMobileDevice = ref(false)

      // 监听打开流程详情事件
      const unsubscribe = useEventBus().on('open-process-detail', data => {
        processInstanceId.value = data.processInstanceId

        // 如果是移动端，跳转到流程页面；否则显示弹窗
        if (isMobileDevice.value) {
          router.push({
            path: '/bpm/processInstance/detail/index',
            query: {
              id: data.processInstanceId,
            },
          })
        } else {
          showProcessDetail.value = true
        }
      })

      // 组件卸载时移除事件监听
      onUnmounted(() => {
        unsubscribe()
      })

      // 设备类型检测和响应式监听
      const updateDeviceType = (deviceType: DeviceType) => {
        isMobileDevice.value = deviceType === DeviceType.MOBILE
      }
      provide('isMobileDevice', isMobileDevice)

      // 在挂载时添加设备类名到body和初始化设备类型
      onMounted(() => {
        document.body.className = `${document.body.className} ${deviceClasses.value}`.trim()

        // 初始化设备类型
        isMobileDevice.value = isMobileDeviceFn()
        console.log('isMobileDevice', isMobileDevice.value)

        // 添加响应式监听器
        responsiveListener.addListener(updateDeviceType)
      })

      onUnmounted(() => {
        // 清理监听器
        responsiveListener.removeListener(updateDeviceType)
      })

      return {
        zhCN, // NaiveUI 中文
        dateZhCN, // NaiveUI 中文日期
        antdLocale: zhCN_antd, // Ant Design Vue 中文
        darkTheme, //  暗黑模式
        themeOverrides, // 响应式主题
        hljs, // 高亮
        showProcessDetail,
        processInstanceId,
      }
    },
  })
</script>
<style scoped>
  .n-config-provider {
    height: 100%;
    width: 100%;
  }
</style>

<style>
  .w-240px {
    width: 240px;
  }

  .ml-10px {
    margin-left: 10px;
  }

  .w-32px {
    width: 32px;
  }

  .h-32px {
    height: 32px;
  }

  .flex {
    display: flex;
  }

  .block {
    display: block;
  }

  .mb-20px {
    margin-bottom: 20px;
  }

  .ml-5px {
    margin-left: 5px;
  }

  /* 日志错误消息样式 */
  .hljs-error {
    color: #f5222d !important; /* 错误消息使用红色 */
    font-weight: bold;
  }

  /* 日志缺失参数消息样式 */
  .hljs-missing {
    color: #faad14 !important; /* 缺失参数使用黄色警告色 */
    font-weight: bold;
  }
  /* 日志完成消息样式 */
  .hljs-complete {
    color: #52c41a !important; /* 完成消息使用绿色 */
    font-weight: bold;
  }
  .hljs-pmsParam {
    color: #2b72be !important; /* 参数使用灰色 */
    font-weight: bold;
  }

  /* 移动端适配全局样式 */
  .device-mobile {
    /* 移动端专用样式 */
  }

  .device-tablet {
    /* 平板专用样式 */
  }

  .device-desktop {
    /* 桌面端专用样式 */
  }

  .touch-device {
    /* 触摸设备通用样式 */
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* 移动端滚动优化 */
  .device-mobile * {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* 移动端表单优化 */
  .device-mobile input,
  .device-mobile textarea,
  .device-mobile select {
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 移动端按钮优化 */
  .device-mobile .n-button {
    min-height: 44px; /* 触摸友好的最小高度 */
  }

  /* 响应式隐藏类 */
  @media (max-width: 767px) {
    .desktop-only {
      display: none !important;
    }
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    .mobile-only,
    .desktop-only {
      display: none !important;
    }
  }

  @media (min-width: 1024px) {
    .mobile-only,
    .tablet-only {
      display: none !important;
    }
  }
</style>
